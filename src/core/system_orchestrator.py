from src.data.phase1_data_pipeline_unification import get_unified_data_service, get_price
#!/usr/bin/env python3
"""
🎯 20 PIP Challenge System Orchestrator
Unified command center that sews together all 8 repositories into a revenue-generating trading system

Architecture:
- Python Core Engine (advanced-trading-system) as the brain
- TypeScript MEV Protection (pumpswap-bot) for transaction safety
- Multi-DEX Router (solana-trading-cli) for optimal pricing
- Smart Money Tracking (handi-cat + SolanaWhaleWatcher) for signals
- Technical Analysis (soltrade) for indicators
- AI Intelligence (solana-ai-trading-bot) for decision making
- MEV Arbitrage (jito-mev-bot) for additional revenue
"""

import asyncio
import json
import logging
import subprocess
import time
import os
import signal
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import threading
from src.agents.capital_manager import CapitalManager
from src.agents.global_risk_manager import GlobalRiskManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class ComponentStatus:
    """Status of a system component"""
    name: str
    type: str  # 'python' | 'typescript' | 'javascript'
    status: str  # 'stopped' | 'starting' | 'running' | 'error'
    process: Optional[subprocess.Popen] = None
    last_heartbeat: Optional[datetime] = None
    revenue_generated: float = 0.0
    error_count: int = 0

@dataclass
class TradingSignal:
    """Unified trading signal format"""
    source: str
    signal_type: str  # 'buy' | 'sell' | 'whale_movement' | 'technical' | 'ai'
    token_address: str
    confidence: float  # 0.0 to 1.0
    amount: Optional[float] = None
    price: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: float = 0.0

class SystemOrchestrator:
    """
    🎯 Master orchestrator that coordinates all trading components
    
    Responsibilities:
    1. Start/stop all components in correct order
    2. Route trading signals between components
    3. Monitor system health and performance
    4. Coordinate revenue generation strategies
    5. Handle failures and recovery
    """
    
    def __init__(self, config_path: str = "orchestrator_config.json"):
        self.config_path = config_path
        self.config = self.load_config()
        self.components: Dict[str, ComponentStatus] = {}
        self.signal_queue = asyncio.Queue()
        self.is_running = False
        self.revenue_target = self.config.get('daily_revenue_target', 0.02)  # 0.02 SOL = 20%
        self.total_revenue = 0.0

        # --- CapitalManager and GlobalRiskManager integration ---
        total_capital = self.config.get('total_capital', 100.0)  # Default: 100 SOL
        risk_limits = self.config.get('risk_limits', {"max_drawdown": 0.2, "max_position": 100000})
        self.capital_manager = CapitalManager(total_capital)
        self.global_risk_manager = GlobalRiskManager(risk_limits)
        logger.info(f"🪙 CapitalManager initialized with total capital: {total_capital}")
        logger.info(f"🛡️ GlobalRiskManager initialized with risk limits: {risk_limits}")
        # ------------------------------------------------------

        # Component definitions
        self.component_definitions = {
            'core_engine': {
                'type': 'python',
                'path': 'advanced-trading-system',
                'command': ['python', 'src/runner.py'],
                'priority': 1,  # Start first
                'revenue_critical': True
            },
            'mev_protection': {
                'type': 'typescript',
                'path': 'pumpswap-bot',
                'command': ['npx', 'ts-node', 'src/bridge_executor.ts'],
                'priority': 2,
                'revenue_critical': True
            },
            'multi_dex': {
                'type': 'typescript', 
                'path': 'solana-trading-cli',
                'command': ['npx', 'ts-node', 'src/jupiter/price_monitor.ts'],
                'priority': 3,
                'revenue_critical': True
            },
            'whale_watcher': {
                'type': 'javascript',
                'path': 'SolanaWhaleWatcher',
                'command': ['node', 'SWW.js'],
                'priority': 4,
                'revenue_critical': False
            },
            'hummingbot_bridge': {
                'type': 'python',
                'path': 'src/agents',
                'command': ['python', 'hummingbot_bridge.py'],
                'priority': 5,
                'revenue_critical': True,
                'description': 'Hummingbot Enterprise Service Bus Bridge - 50+ Exchange Connectors'
            },
            'smart_money': {
                'type': 'typescript',
                'path': 'handi-cat_wallet-tracker',
                'command': ['npx', 'ts-node', 'src/main.ts'],
                'priority': 5,
                'revenue_critical': False
            },
            'technical_analysis': {
                'type': 'python',
                'path': 'soltrade',
                'command': ['python', 'soltrade.py'],
                'priority': 6,
                'revenue_critical': False
            },
            'mev_arbitrage': {
                'type': 'typescript',
                'path': 'jito-mev-bot',
                'command': ['npm', 'start'],
                'priority': 7,
                'revenue_critical': False
            }
        }
        
        logger.info("🎯 System Orchestrator initialized")
    
    def load_config(self) -> Dict[str, Any]:
        """Load orchestrator configuration"""
        default_config = {
            'daily_revenue_target': 0.02,  # 20% of 0.1 SOL
            'max_concurrent_trades': 3,
            'risk_per_trade': 0.005,  # 5% max risk per trade
            'enable_mev_protection': True,
            'enable_smart_money': True,
            'enable_technical_analysis': True,
            'enable_arbitrage': False,  # Start disabled
            'heartbeat_interval': 30,  # seconds
            'restart_on_failure': True
        }
        
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                    default_config.update(config)
            except Exception as e:
                logger.warning(f"Failed to load config: {e}, using defaults")
        
        return default_config
    
    async def start_system(self):
        """🚀 Start the entire trading system"""
        logger.info("🚀 Starting 20 PIP Challenge System Orchestrator")
        logger.info(f"💰 Daily Revenue Target: {self.revenue_target} SOL")
        
        self.is_running = True
        
        # Start components in priority order
        await self.start_components()
        
        # Start monitoring tasks
        tasks = [
            asyncio.create_task(self.monitor_components()),
            asyncio.create_task(self.process_signals()),
            asyncio.create_task(self.revenue_monitor()),
            asyncio.create_task(self.health_checker())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"System error: {e}")
        finally:
            await self.shutdown_system()
    
    async def start_components(self):
        """Start all components in correct order"""
        # Sort by priority
        sorted_components = sorted(
            self.component_definitions.items(),
            key=lambda x: x[1]['priority']
        )
        
        for name, definition in sorted_components:
            if not self.should_start_component(name, definition):
                continue
                
            logger.info(f"🔄 Starting {name}...")
            await self.start_component(name, definition)
            
            # Wait between starts to avoid resource conflicts
            await asyncio.sleep(2)
    
    def should_start_component(self, name: str, definition: Dict[str, Any]) -> bool:
        """Determine if component should be started based on config"""
        if name == 'mev_protection' and not self.config.get('enable_mev_protection'):
            return False
        if name == 'smart_money' and not self.config.get('enable_smart_money'):
            return False
        if name == 'technical_analysis' and not self.config.get('enable_technical_analysis'):
            return False
        if name == 'mev_arbitrage' and not self.config.get('enable_arbitrage'):
            return False
        return True
    
    async def start_component(self, name: str, definition: Dict[str, Any]):
        """Start a single component"""
        try:
            # Change to component directory
            cwd = Path(definition['path'])
            if not cwd.exists():
                logger.error(f"❌ Component path not found: {cwd}")
                return
            
            # Start process
            process = subprocess.Popen(
                definition['command'],
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Create component status
            self.components[name] = ComponentStatus(
                name=name,
                type=definition['type'],
                status='starting',
                process=process,
                last_heartbeat=datetime.now()
            )
            
            logger.info(f"✅ {name} started (PID: {process.pid})")
            
            # Give it time to initialize
            await asyncio.sleep(3)
            
            # Check if still running
            if process.poll() is None:
                self.components[name].status = 'running'
                logger.info(f"🟢 {name} is running")
            else:
                self.components[name].status = 'error'
                logger.error(f"❌ {name} failed to start")
                
        except Exception as e:
            logger.error(f"❌ Failed to start {name}: {e}")
            if name in self.components:
                self.components[name].status = 'error'
    
    async def monitor_components(self):
        """Monitor component health and restart if needed"""
        while self.is_running:
            try:
                for name, component in self.components.items():
                    if component.process and component.process.poll() is not None:
                        # Process died
                        logger.warning(f"⚠️ {name} process died")
                        component.status = 'error'
                        component.error_count += 1
                        
                        # Restart if enabled and not too many failures
                        if (self.config.get('restart_on_failure') and 
                            component.error_count < 3):
                            logger.info(f"🔄 Restarting {name}...")
                            definition = self.component_definitions[name]
                            await self.start_component(name, definition)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error monitoring components: {e}")
                await asyncio.sleep(10)
    
    async def process_signals(self):
        """Process trading signals from all components"""
        while self.is_running:
            try:
                # Wait for signals (with timeout to allow shutdown)
                try:
                    signal = await asyncio.wait_for(
                        self.signal_queue.get(), 
                        timeout=1.0
                    )
                    await self.handle_trading_signal(signal)
                except asyncio.TimeoutError:
                    continue
                    
            except Exception as e:
                logger.error(f"Error processing signals: {e}")
                await asyncio.sleep(1)
    
    async def handle_trading_signal(self, signal: TradingSignal):
        """Handle incoming trading signal"""
        logger.info(f"📡 Signal from {signal.source}: {signal.signal_type} "
                   f"for {signal.token_address[:8]}... (confidence: {signal.confidence})")
        
        # Route signal based on type and confidence
        if signal.confidence >= 0.7 and signal.signal_type in ['buy', 'sell']:
            # High confidence trade signal - execute
            await self.execute_trade_signal(signal)
        elif signal.signal_type == 'whale_movement':
            # Whale movement - analyze and potentially follow
            await self.analyze_whale_signal(signal)
        elif signal.signal_type == 'technical':
            # Technical analysis signal - combine with other signals
            await self.process_technical_signal(signal)
    
    async def execute_trade_signal(self, signal: TradingSignal):
        """Execute a trade signal through the core engine"""
        try:
            # Send to core engine via file-based communication
            signal_file = f"signals/trade_signal_{int(time.time())}.json"
            os.makedirs("signals", exist_ok=True)
            
            with open(signal_file, 'w') as f:
                json.dump(asdict(signal), f, indent=2)
            
            logger.info(f"💰 Trade signal queued: {signal_file}")
            
        except Exception as e:
            logger.error(f"Failed to execute trade signal: {e}")

    async def analyze_whale_signal(self, signal: TradingSignal):
        """Analyze whale movement signal"""
        try:
            metadata = signal.metadata or {}
            whale_amount = metadata.get('amount', 0)

            # Only follow large whale movements
            if whale_amount >= self.config.get('whale_movement_threshold', 10000):
                logger.info(f"🐋 Large whale movement detected: {whale_amount} tokens")

                # Create follow signal with reduced confidence
                follow_signal = TradingSignal(
                    source='whale_follower',
                    signal_type=signal.signal_type,
                    token_address=signal.token_address,
                    confidence=signal.confidence * 0.7,  # Reduce confidence
                    amount=min(whale_amount * 0.01, 0.005),  # 1% of whale or max risk
                    metadata={'whale_follow': True, 'original_whale_amount': whale_amount}
                )

                await self.signal_queue.put(follow_signal)

        except Exception as e:
            logger.error(f"Error analyzing whale signal: {e}")

    async def process_technical_signal(self, signal: TradingSignal):
        """Process technical analysis signal"""
        try:
            # Store technical signals for combination with other signals
            signal_key = f"tech_{signal.token_address}"

            # Create signals directory if it doesn't exist
            os.makedirs("signals/technical", exist_ok=True)

            # Save technical signal
            tech_file = f"signals/technical/{signal_key}_{int(time.time())}.json"
            with open(tech_file, 'w') as f:
                json.dump(asdict(signal), f, indent=2)

            logger.info(f"📊 Technical signal stored: {signal.signal_type} "
                       f"confidence {signal.confidence}")

        except Exception as e:
            logger.error(f"Error processing technical signal: {e}")

    async def revenue_monitor(self):
        """Monitor revenue generation and targets"""
        while self.is_running:
            try:
                # Calculate total revenue from all components
                total_revenue = sum(c.revenue_generated for c in self.components.values())
                
                if total_revenue != self.total_revenue:
                    self.total_revenue = total_revenue
                    progress = (total_revenue / self.revenue_target) * 100
                    
                    logger.info(f"💰 Revenue Update: {total_revenue:.4f} SOL "
                               f"({progress:.1f}% of daily target)")
                    
                    if total_revenue >= self.revenue_target:
                        logger.info("🎉 DAILY TARGET ACHIEVED! Consider stopping for the day.")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error monitoring revenue: {e}")
                await asyncio.sleep(60)
    
    async def health_checker(self):
        """Perform system health checks"""
        while self.is_running:
            try:
                running_count = sum(1 for c in self.components.values() 
                                  if c.status == 'running')
                total_count = len(self.components)
                
                logger.info(f"🏥 System Health: {running_count}/{total_count} "
                           f"components running")
                
                # Check if critical components are down
                critical_down = [
                    name for name, comp in self.components.items()
                    if (self.component_definitions[name].get('revenue_critical') and 
                        comp.status != 'running')
                ]
                
                if critical_down:
                    logger.warning(f"⚠️ Critical components down: {critical_down}")
                
                await asyncio.sleep(self.config.get('heartbeat_interval', 30))
                
            except Exception as e:
                logger.error(f"Error in health check: {e}")
                await asyncio.sleep(30)
    
    async def shutdown_system(self):
        """Gracefully shutdown all components"""
        logger.info("🛑 Shutting down System Orchestrator...")
        self.is_running = False
        
        for name, component in self.components.items():
            if component.process and component.process.poll() is None:
                logger.info(f"🛑 Stopping {name}...")
                try:
                    component.process.terminate()
                    await asyncio.sleep(2)
                    if component.process.poll() is None:
                        component.process.kill()
                except Exception as e:
                    logger.error(f"Error stopping {name}: {e}")
        
        logger.info("✅ System shutdown complete")

# Signal handlers for graceful shutdown
def signal_handler(signum, frame):
    logger.info(f"Received signal {signum}, initiating shutdown...")
    # Set a global flag that the main loop can check
    global shutdown_requested
    shutdown_requested = True

shutdown_requested = False

async def main():
    """Main entry point"""
    global shutdown_requested
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    orchestrator = SystemOrchestrator()
    
    try:
        # Start the system
        task = asyncio.create_task(orchestrator.start_system())
        
        # Wait for shutdown signal
        while not shutdown_requested and not task.done():
            await asyncio.sleep(0.1)
        
        if not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"System error: {e}")
    finally:
        await orchestrator.shutdown_system()

if __name__ == "__main__":
    print("🎯 20 PIP Challenge System Orchestrator")
    print("=" * 50)
    asyncio.run(main())
