#!/usr/bin/env python3
"""
🤖 HUMMINGBOT ENTERPRISE SERVICE BUS BRIDGE
Connects Hummingbot's 50+ exchange connectors to NEXUS Redis Signal Bus

INTEGRATION ARCHITECTURE:
NEXUS Redis Bus → HummingbotBridge → Hummingbot Script → 50+ Exchanges
                ←                  ←                  ←

CAPABILITIES:
- Bidirectional communication with Hummingbot
- Command translation (NEXUS signals → Hummingbot orders)
- Market data ingestion (Hummingbot → NEXUS channels)
- Execution reporting (Trade results → execution_results channel)
- Health monitoring and error recovery

CHANNELS:
- SUBSCRIBE: trading_signals (receives trade commands)
- PUBLISH: execution_results, hummingbot_market_data, component_heartbeat
"""

import asyncio
import json
import logging
import time
import os
import subprocess
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from agents.phase1_redis_signal_bus import get_unified_signal_bus, SignalType, SignalPriority

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HummingbotCommand(Enum):
    """Hummingbot command types"""
    BUY = "buy"
    SELL = "sell"
    CANCEL = "cancel"
    STATUS = "status"
    BALANCE = "balance"
    HISTORY = "history"

@dataclass
class HummingbotOrder:
    """Hummingbot order structure"""
    action: str  # BUY/SELL
    pair: str    # e.g., BTC-USDT
    amount: float
    price: Optional[float] = None  # Market order if None
    exchange: str = "binance"  # Default exchange
    order_type: str = "market"  # market/limit

@dataclass
class ExecutionResult:
    """Trade execution result"""
    order_id: str
    pair: str
    action: str
    amount: float
    price: float
    status: str  # filled/partial/failed
    timestamp: float
    exchange: str
    fees: float = 0.0

class HummingbotBridge:
    """Bridge between NEXUS Redis Bus and Hummingbot"""
    
    def __init__(self, hummingbot_path: str = "external/hummingbot"):
        self.hummingbot_path = Path(hummingbot_path)
        self.signal_bus = get_unified_signal_bus()
        self.is_running = False
        self.component_name = "hummingbot_bridge"
        
        # Communication files
        self.command_file = self.hummingbot_path / "nexus_commands.json"
        self.response_file = self.hummingbot_path / "nexus_responses.json"
        self.market_data_file = self.hummingbot_path / "nexus_market_data.json"
        
        # Hummingbot process
        self.hummingbot_process = None
        
        logger.info("🤖 Hummingbot Bridge initialized")
        logger.info(f"📁 Hummingbot path: {self.hummingbot_path}")
    
    async def start(self):
        """Start the Hummingbot bridge"""
        logger.info("🚀 Starting Hummingbot Bridge...")
        
        # Connect to Redis signal bus
        if not await self._connect_to_signal_bus():
            logger.error("❌ Failed to connect to Redis signal bus")
            return False
        
        # Start Hummingbot with NEXUS script
        if not await self._start_hummingbot():
            logger.error("❌ Failed to start Hummingbot")
            return False
        
        self.is_running = True
        
        # Start monitoring tasks
        tasks = [
            asyncio.create_task(self._monitor_signals()),
            asyncio.create_task(self._monitor_responses()),
            asyncio.create_task(self._monitor_market_data()),
            asyncio.create_task(self._send_heartbeat())
        ]
        
        logger.info("✅ Hummingbot Bridge started successfully")
        logger.info("🔌 Connected to NEXUS Redis Signal Bus")
        logger.info("🤖 Hummingbot instance running with NEXUS script")
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"❌ Bridge error: {e}")
        finally:
            await self.shutdown()
    
    async def _connect_to_signal_bus(self) -> bool:
        """Connect to NEXUS Redis signal bus"""
        try:
            # Connect to Redis
            if not self.signal_bus.redis_client:
                await self.signal_bus.connect()

            # Register signal handler for trading signals
            self.signal_bus.register_handler(
                SignalType.BUY_SIGNAL,
                self._handle_trading_signal
            )
            self.signal_bus.register_handler(
                SignalType.SELL_SIGNAL,
                self._handle_trading_signal
            )

            logger.info("📡 Connected to NEXUS Redis signal bus")
            logger.info("🔌 Registered handlers for BUY/SELL signals")
            return True
        except Exception as e:
            logger.error(f"❌ Signal bus connection failed: {e}")
            return False
    
    async def _start_hummingbot(self) -> bool:
        """Start Hummingbot with NEXUS integration script"""
        try:
            # Ensure command/response files exist
            self.command_file.touch()
            self.response_file.touch()
            self.market_data_file.touch()
            
            # Start Hummingbot with Docker
            docker_cmd = [
                "docker-compose", "-f", str(self.hummingbot_path / "docker-compose.yml"),
                "up", "-d"
            ]
            
            result = subprocess.run(docker_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"❌ Docker start failed: {result.stderr}")
                return False
            
            logger.info("🐳 Hummingbot Docker container started")
            
            # Wait for Hummingbot to initialize
            await asyncio.sleep(10)
            
            return True
        except Exception as e:
            logger.error(f"❌ Hummingbot start failed: {e}")
            return False
    
    async def _handle_trading_signal(self, signal):
        """Handle incoming trading signals from NEXUS"""
        try:
            # Extract signal data
            signal_data = signal.data if hasattr(signal, 'data') else signal
            logger.info(f"📨 Received trading signal: {signal_data}")

            # Check if signal is for Hummingbot
            target_system = signal_data.get('target_system', 'all')
            if target_system not in ['hummingbot', 'all']:
                logger.debug(f"🔄 Signal not for Hummingbot: {target_system}")
                return

            # Parse signal into Hummingbot order
            order = self._parse_signal_to_order(signal_data)
            if not order:
                logger.warning("⚠️ Failed to parse signal to order")
                return

            # Send command to Hummingbot
            await self._send_command_to_hummingbot(order)

        except Exception as e:
            logger.error(f"❌ Signal handling error: {e}")
    
    def _parse_signal_to_order(self, signal_data: Dict[str, Any]) -> Optional[HummingbotOrder]:
        """Parse NEXUS signal into Hummingbot order"""
        try:
            action = signal_data.get('action', '').upper()
            if action not in ['BUY', 'SELL']:
                return None
            
            return HummingbotOrder(
                action=action,
                pair=signal_data.get('pair', 'BTC-USDT'),
                amount=float(signal_data.get('amount', 0.01)),
                price=signal_data.get('price'),  # None for market orders
                exchange=signal_data.get('exchange', 'binance'),
                order_type=signal_data.get('order_type', 'market')
            )
        except Exception as e:
            logger.error(f"❌ Signal parsing error: {e}")
            return None
    
    async def _send_command_to_hummingbot(self, order: HummingbotOrder):
        """Send command to Hummingbot via atomic file write"""
        try:
            command = {
                'timestamp': time.time(),
                'command_id': f"nexus_{int(time.time())}_{hash(str(order.pair))%10000}",
                'action': order.action,
                'pair': order.pair,
                'amount': order.amount,
                'price': order.price,
                'exchange': order.exchange,
                'order_type': order.order_type
            }

            # Atomic write to prevent race conditions
            await self._atomic_write_json(self.command_file, command)

            logger.info(f"📤 Sent command to Hummingbot: {order.action} {order.amount} {order.pair}")

        except Exception as e:
            logger.error(f"❌ Command sending error: {e}")

    async def _atomic_write_json(self, file_path: Path, data: Dict[str, Any]):
        """Atomic write to prevent file corruption"""
        temp_file = file_path.with_suffix('.tmp')
        try:
            # Write to temporary file first
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
                f.flush()
                os.fsync(f.fileno())

            # Atomic move
            temp_file.replace(file_path)

        except Exception as e:
            if temp_file.exists():
                temp_file.unlink()
            raise e

    async def _atomic_read_json(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Atomic read with error handling"""
        if not file_path.exists():
            return None

        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return None
        except Exception as e:
            logger.error(f"❌ File read error: {e}")
            return None
    
    async def _monitor_responses(self):
        """Monitor Hummingbot execution responses"""
        last_modified = 0
        
        while self.is_running:
            try:
                if self.response_file.exists():
                    current_modified = self.response_file.stat().st_mtime
                    
                    if current_modified > last_modified:
                        last_modified = current_modified

                        # Atomic read and process response
                        response_data = await self._atomic_read_json(self.response_file)
                        if response_data:
                            await self._process_execution_response(response_data)
                
                await asyncio.sleep(1)  # Check every second
                
            except Exception as e:
                logger.error(f"❌ Response monitoring error: {e}")
                await asyncio.sleep(5)
    
    async def _process_execution_response(self, response_data: Dict[str, Any]):
        """Process execution response from Hummingbot"""
        try:
            # Create execution result
            result = ExecutionResult(
                order_id=response_data.get('order_id', 'unknown'),
                pair=response_data.get('pair', 'unknown'),
                action=response_data.get('action', 'unknown'),
                amount=float(response_data.get('amount', 0)),
                price=float(response_data.get('price', 0)),
                status=response_data.get('status', 'unknown'),
                timestamp=response_data.get('timestamp', time.time()),
                exchange=response_data.get('exchange', 'unknown'),
                fees=float(response_data.get('fees', 0))
            )
            
            # Publish to execution_results channel
            execution_signal = {
                'signal_type': SignalType.EXECUTION_RESULT.value,
                'priority': SignalPriority.HIGH.value,
                'source': self.component_name,
                'timestamp': result.timestamp,
                'data': {
                    'order_id': result.order_id,
                    'pair': result.pair,
                    'action': result.action,
                    'amount': result.amount,
                    'price': result.price,
                    'status': result.status,
                    'exchange': result.exchange,
                    'fees': result.fees
                }
            }

            await self.signal_bus.publish_to_channel('execution_results', execution_signal)
            
            logger.info(f"✅ Execution result published: {result.action} {result.amount} {result.pair} @ {result.price}")
            
        except Exception as e:
            logger.error(f"❌ Response processing error: {e}")
    
    async def _monitor_market_data(self):
        """Monitor market data from Hummingbot"""
        last_modified = 0
        
        while self.is_running:
            try:
                if self.market_data_file.exists():
                    current_modified = self.market_data_file.stat().st_mtime
                    
                    if current_modified > last_modified:
                        last_modified = current_modified

                        # Atomic read and publish market data
                        market_data = await self._atomic_read_json(self.market_data_file)
                        if market_data:
                            await self._publish_market_data(market_data)
                
                await asyncio.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                logger.error(f"❌ Market data monitoring error: {e}")
                await asyncio.sleep(5)
    
    async def _publish_market_data(self, market_data: Dict[str, Any]):
        """Publish market data to hummingbot_market_data channel"""
        try:
            market_signal = {
                'signal_type': 'market_data',
                'source': self.component_name,
                'timestamp': time.time(),
                'data': market_data
            }

            await self.signal_bus.publish_to_channel('hummingbot_market_data', market_signal)

            logger.debug(f"📊 Market data published: {len(market_data)} exchanges")

        except Exception as e:
            logger.error(f"❌ Market data publishing error: {e}")

    async def _send_heartbeat(self):
        """Send periodic heartbeat to signal bus"""
        while self.is_running:
            try:
                heartbeat_signal = {
                    'signal_type': 'heartbeat',
                    'source': self.component_name,
                    'timestamp': time.time(),
                    'data': {
                        'status': 'healthy',
                        'uptime': time.time() - (getattr(self, 'start_time', time.time())),
                        'component': 'hummingbot_bridge'
                    }
                }

                await self.signal_bus.publish_to_channel('component_heartbeat', heartbeat_signal)

                await asyncio.sleep(60)  # Heartbeat every minute

            except Exception as e:
                logger.error(f"❌ Heartbeat error: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_signals(self):
        """Monitor for shutdown signals"""
        while self.is_running:
            await asyncio.sleep(1)
    
    async def shutdown(self):
        """Shutdown the bridge"""
        logger.info("🛑 Shutting down Hummingbot Bridge...")
        self.is_running = False
        
        # Stop Hummingbot
        if self.hummingbot_process:
            self.hummingbot_process.terminate()
        
        # Stop Docker container
        try:
            docker_cmd = [
                "docker-compose", "-f", str(self.hummingbot_path / "docker-compose.yml"),
                "down"
            ]
            subprocess.run(docker_cmd, capture_output=True)
            logger.info("🐳 Hummingbot Docker container stopped")
        except Exception as e:
            logger.warning(f"⚠️ Docker stop warning: {e}")

# Singleton instance
_hummingbot_bridge = None

def get_hummingbot_bridge() -> HummingbotBridge:
    """Get singleton Hummingbot bridge instance"""
    global _hummingbot_bridge
    if _hummingbot_bridge is None:
        _hummingbot_bridge = HummingbotBridge()
    return _hummingbot_bridge

async def main():
    """Test the Hummingbot bridge"""
    bridge = get_hummingbot_bridge()
    await bridge.start()

if __name__ == "__main__":
    asyncio.run(main())
