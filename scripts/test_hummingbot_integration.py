#!/usr/bin/env python3
"""
🚀 HUMMINGBOT INTEGRATION TEST RUNNER
Quick validation script for Hummingbot Enterprise Service Bus integration

USAGE:
    python scripts/test_hummingbot_integration.py

VALIDATION STEPS:
1. Check Redis connection
2. Verify Hummingbot Docker setup
3. Test signal publishing
4. Validate bridge connectivity
5. Generate integration report

SAFETY:
- No real trading
- Testnet only
- Quick validation (< 2 minutes)
"""

import asyncio
import json
import time
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.agents.phase1_redis_signal_bus import get_unified_signal_bus
from src.agents.hummingbot_bridge import get_hummingbot_bridge

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickIntegrationValidator:
    """Quick validation of Hummingbot integration"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
        
    async def run_validation(self):
        """Run quick validation checks"""
        logger.info("🚀 Starting Hummingbot Integration Validation")
        logger.info("=" * 60)
        
        try:
            # Step 1: Redis Connection
            await self._check_redis_connection()
            
            # Step 2: Hummingbot Setup
            await self._check_hummingbot_setup()
            
            # Step 3: Bridge Components
            await self._check_bridge_components()
            
            # Step 4: Signal Flow
            await self._check_signal_flow()
            
            # Step 5: Generate Report
            self._generate_report()
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            self.results['overall_status'] = 'FAILED'
            self.results['error'] = str(e)
        
        return self.results
    
    async def _check_redis_connection(self):
        """Check Redis connection"""
        logger.info("📡 Step 1: Checking Redis Connection...")
        
        try:
            signal_bus = get_unified_signal_bus()
            
            # Test connection
            if signal_bus.redis_client:
                # Try a simple operation
                test_key = "nexus:test:connection"
                signal_bus.redis_client.set(test_key, "test_value", ex=10)
                value = signal_bus.redis_client.get(test_key)
                
                if value == b"test_value":
                    self.results['redis_connection'] = True
                    logger.info("✅ Redis connection successful")
                else:
                    raise Exception("Redis read/write test failed")
            else:
                raise Exception("Redis client not initialized")
                
        except Exception as e:
            self.results['redis_connection'] = False
            logger.error(f"❌ Redis connection failed: {e}")
            raise
    
    async def _check_hummingbot_setup(self):
        """Check Hummingbot setup"""
        logger.info("🤖 Step 2: Checking Hummingbot Setup...")
        
        setup_checks = {
            'docker_compose': False,
            'integration_script': False,
            'directory_structure': False
        }
        
        # Check Docker Compose
        docker_compose_path = Path("external/hummingbot/docker-compose.yml")
        if docker_compose_path.exists():
            setup_checks['docker_compose'] = True
            logger.info("✅ Docker Compose configuration found")
        else:
            logger.warning("⚠️ Docker Compose configuration not found")
        
        # Check Integration Script
        script_path = Path("external/hummingbot/scripts/nexus_integration_script.py")
        if script_path.exists():
            setup_checks['integration_script'] = True
            logger.info("✅ NEXUS integration script found")
        else:
            logger.warning("⚠️ NEXUS integration script not found")
        
        # Check Directory Structure
        required_dirs = [
            "external/hummingbot/conf",
            "external/hummingbot/logs",
            "external/hummingbot/scripts"
        ]
        
        all_dirs_exist = all(Path(dir_path).exists() for dir_path in required_dirs)
        if all_dirs_exist:
            setup_checks['directory_structure'] = True
            logger.info("✅ Directory structure validated")
        else:
            logger.warning("⚠️ Some required directories missing")
        
        self.results['hummingbot_setup'] = setup_checks
        
        # Overall setup status
        if all(setup_checks.values()):
            logger.info("✅ Hummingbot setup complete")
        else:
            logger.warning("⚠️ Hummingbot setup incomplete")
    
    async def _check_bridge_components(self):
        """Check bridge components"""
        logger.info("🌉 Step 3: Checking Bridge Components...")
        
        component_checks = {
            'bridge_module': False,
            'orchestrator_integration': False,
            'signal_bus_integration': False
        }
        
        # Check Bridge Module
        try:
            bridge = get_hummingbot_bridge()
            if bridge:
                component_checks['bridge_module'] = True
                logger.info("✅ Hummingbot bridge module loaded")
        except Exception as e:
            logger.warning(f"⚠️ Bridge module issue: {e}")
        
        # Check Orchestrator Integration
        try:
            from src.core.system_orchestrator import SystemOrchestrator
            orchestrator = SystemOrchestrator()
            
            if 'hummingbot_bridge' in orchestrator.component_definitions:
                component_checks['orchestrator_integration'] = True
                logger.info("✅ Orchestrator integration confirmed")
            else:
                logger.warning("⚠️ Bridge not found in orchestrator")
        except Exception as e:
            logger.warning(f"⚠️ Orchestrator check failed: {e}")
        
        # Check Signal Bus Integration
        try:
            signal_bus = get_unified_signal_bus()
            if 'trading_signals' in signal_bus.channels:
                component_checks['signal_bus_integration'] = True
                logger.info("✅ Signal bus integration confirmed")
        except Exception as e:
            logger.warning(f"⚠️ Signal bus check failed: {e}")
        
        self.results['bridge_components'] = component_checks
        
        if all(component_checks.values()):
            logger.info("✅ Bridge components validated")
        else:
            logger.warning("⚠️ Some bridge components need attention")
    
    async def _check_signal_flow(self):
        """Check signal flow"""
        logger.info("📨 Step 4: Checking Signal Flow...")
        
        signal_checks = {
            'signal_publishing': False,
            'channel_subscription': False,
            'message_format': False
        }
        
        try:
            signal_bus = get_unified_signal_bus()
            
            # Test signal publishing
            test_signal = {
                'type': 'validation_test',
                'timestamp': time.time(),
                'test_id': 'hummingbot_integration_test'
            }
            
            await signal_bus.publish_to_channel('trading_signals', test_signal)
            signal_checks['signal_publishing'] = True
            logger.info("✅ Signal publishing successful")
            
            # Test channel subscription (simplified)
            if 'trading_signals' in signal_bus.channels:
                signal_checks['channel_subscription'] = True
                logger.info("✅ Channel subscription available")
            
            # Test message format
            if isinstance(test_signal, dict) and 'timestamp' in test_signal:
                signal_checks['message_format'] = True
                logger.info("✅ Message format validated")
            
        except Exception as e:
            logger.warning(f"⚠️ Signal flow check failed: {e}")
        
        self.results['signal_flow'] = signal_checks
        
        if all(signal_checks.values()):
            logger.info("✅ Signal flow validated")
        else:
            logger.warning("⚠️ Signal flow needs attention")
    
    def _generate_report(self):
        """Generate validation report"""
        logger.info("📋 Step 5: Generating Validation Report...")
        
        # Calculate overall metrics
        total_checks = 0
        passed_checks = 0
        
        for category, checks in self.results.items():
            if isinstance(checks, dict):
                for check, result in checks.items():
                    total_checks += 1
                    if result:
                        passed_checks += 1
            elif isinstance(checks, bool):
                total_checks += 1
                if checks:
                    passed_checks += 1
        
        success_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0
        
        # Determine overall status
        if success_rate >= 90:
            overall_status = "EXCELLENT"
            status_emoji = "🟢"
        elif success_rate >= 75:
            overall_status = "GOOD"
            status_emoji = "🟡"
        elif success_rate >= 50:
            overall_status = "NEEDS_WORK"
            status_emoji = "🟠"
        else:
            overall_status = "CRITICAL"
            status_emoji = "🔴"
        
        # Add summary to results
        self.results['validation_summary'] = {
            'total_checks': total_checks,
            'passed_checks': passed_checks,
            'success_rate': success_rate,
            'overall_status': overall_status,
            'duration': time.time() - self.start_time,
            'timestamp': time.time()
        }
        
        # Print summary
        logger.info("=" * 60)
        logger.info("📊 VALIDATION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"{status_emoji} Overall Status: {overall_status}")
        logger.info(f"📈 Success Rate: {success_rate:.1f}%")
        logger.info(f"✅ Passed: {passed_checks}/{total_checks}")
        logger.info(f"⏱️ Duration: {time.time() - self.start_time:.1f}s")
        logger.info("=" * 60)
        
        # Recommendations
        recommendations = self._generate_recommendations()
        if recommendations:
            logger.info("💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"   {i}. {rec}")
            logger.info("=" * 60)
    
    def _generate_recommendations(self):
        """Generate recommendations based on validation results"""
        recommendations = []
        
        # Check Redis connection
        if not self.results.get('redis_connection', False):
            recommendations.append("Start Redis server: redis-server")
        
        # Check Hummingbot setup
        hb_setup = self.results.get('hummingbot_setup', {})
        if not hb_setup.get('docker_compose', False):
            recommendations.append("Set up Hummingbot Docker configuration")
        if not hb_setup.get('integration_script', False):
            recommendations.append("Deploy NEXUS integration script to Hummingbot")
        
        # Check bridge components
        bridge_components = self.results.get('bridge_components', {})
        if not bridge_components.get('orchestrator_integration', False):
            recommendations.append("Add Hummingbot bridge to system orchestrator")
        
        # Check signal flow
        signal_flow = self.results.get('signal_flow', {})
        if not all(signal_flow.values()):
            recommendations.append("Verify signal bus configuration and channels")
        
        # Success case
        if not recommendations:
            recommendations.append("System ready for Hummingbot integration testing")
            recommendations.append("Proceed with full integration test: python tests/test_hummingbot_integration.py")
        
        return recommendations

async def main():
    """Main validation function"""
    print("🤖 HUMMINGBOT INTEGRATION VALIDATOR")
    print("=" * 60)
    print("Quick validation of Hummingbot Enterprise Service Bus integration")
    print("This will check system readiness without starting actual trading")
    print("=" * 60)
    
    validator = QuickIntegrationValidator()
    results = await validator.run_validation()
    
    # Save results to file
    results_file = Path("logs/hummingbot_integration_validation.json")
    results_file.parent.mkdir(exist_ok=True)
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    # Return appropriate exit code
    success_rate = results.get('validation_summary', {}).get('success_rate', 0)
    return 0 if success_rate >= 75 else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
