#!/usr/bin/env python3
"""
🧪 HUMMINGBOT UNIT TEST RUNNER
Runs unit tests for Hummingbot integration components

USAGE:
    python scripts/run_hummingbot_unit_tests.py

TESTS:
- Atomic file I/O operations
- Concurrent access handling
- File corruption recovery
- Communication protocol validation
"""

import sys
import subprocess
import time
from pathlib import Path

def run_unit_tests():
    """Run Hummingbot unit tests"""
    print("🧪 HUMMINGBOT INTEGRATION UNIT TESTS")
    print("=" * 60)
    
    # Test file path
    test_file = Path(__file__).parent.parent / "tests" / "test_hummingbot_file_communication.py"
    
    if not test_file.exists():
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"📁 Running tests from: {test_file}")
    print("=" * 60)
    
    try:
        # Run pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            str(test_file), 
            "-v", 
            "--tb=short",
            "--color=yes"
        ], capture_output=True, text=True)
        
        # Print output
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        # Check result
        if result.returncode == 0:
            print("=" * 60)
            print("✅ ALL UNIT TESTS PASSED")
            print("🚀 File communication system is ready for integration testing")
            return True
        else:
            print("=" * 60)
            print("❌ SOME TESTS FAILED")
            print("🔧 Fix issues before proceeding to integration testing")
            return False
            
    except FileNotFoundError:
        print("❌ pytest not found. Install with: pip install pytest")
        return False
    except Exception as e:
        print(f"❌ Test execution error: {e}")
        return False

def run_simple_validation():
    """Run simple validation without pytest"""
    print("🔧 SIMPLE VALIDATION (No pytest)")
    print("=" * 60)
    
    try:
        # Add project root to path
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        # Import test classes
        from tests.test_hummingbot_file_communication import TestAtomicFileOperations
        
        # Run basic tests
        test_instance = TestAtomicFileOperations()
        
        tests = [
            ("Basic Write/Read", test_instance.test_basic_atomic_write_read),
            ("File Corruption Recovery", test_instance.test_file_corruption_recovery),
            ("Nonexistent File Handling", test_instance.test_nonexistent_file_handling),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"🧪 Running: {test_name}")
                test_instance.setup_method()
                test_func()
                test_instance.teardown_method()
                print(f"✅ {test_name}: PASSED")
                passed += 1
            except Exception as e:
                print(f"❌ {test_name}: FAILED - {e}")
                try:
                    test_instance.teardown_method()
                except:
                    pass
        
        print("=" * 60)
        print(f"📊 Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("✅ BASIC VALIDATION PASSED")
            return True
        else:
            print("❌ SOME TESTS FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def main():
    """Main test runner"""
    start_time = time.time()
    
    # Try pytest first, fall back to simple validation
    success = run_unit_tests()
    
    if not success:
        print("\n" + "=" * 60)
        print("🔄 Falling back to simple validation...")
        print("=" * 60)
        success = run_simple_validation()
    
    duration = time.time() - start_time
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS")
    print("=" * 60)
    print(f"⏱️ Duration: {duration:.2f} seconds")
    
    if success:
        print("🎯 Status: READY FOR INTEGRATION")
        print("📋 Next Steps:")
        print("   1. Complete system orchestrator integration")
        print("   2. Run full integration test")
        print("   3. Test with real Hummingbot instance")
        return 0
    else:
        print("🚨 Status: NEEDS FIXES")
        print("📋 Required Actions:")
        print("   1. Fix failing unit tests")
        print("   2. Verify file I/O operations")
        print("   3. Re-run tests until all pass")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
