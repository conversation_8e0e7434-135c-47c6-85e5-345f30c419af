#!/usr/bin/env python3
"""
🔧 REDIS HEALTH CHECK - RELIABLE VERSION
Fixed Redis connection validation that matches redis-cli ping behavior

USAGE:
    python scripts/test_redis_health.py

VALIDATION:
- Direct Redis connection test
- Matches redis-cli ping behavior
- Reliable true/false reporting
"""

import sys
import time
import redis
from pathlib import Path

def test_redis_direct():
    """Test Redis connection directly"""
    try:
        # Direct Redis connection
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # Test ping
        result = r.ping()
        
        if result:
            print("✅ Direct Redis Connection: WORKING")
            
            # Test read/write
            test_key = "nexus:health:test"
            r.set(test_key, "validation_test", ex=10)
            value = r.get(test_key)
            
            if value == "validation_test":
                print("✅ Redis Read/Write: WORKING")
                r.delete(test_key)
                return True
            else:
                print("❌ Redis Read/Write: FAILED")
                return False
        else:
            print("❌ Direct Redis Connection: FAILED")
            return False
            
    except redis.ConnectionError:
        print("❌ Redis Connection Error: Server not running")
        return False
    except Exception as e:
        print(f"❌ Redis Error: {e}")
        return False

def test_nexus_signal_bus():
    """Test NEXUS signal bus Redis connection"""
    try:
        # Add project root to path
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        from src.agents.phase1_redis_signal_bus import get_unified_signal_bus
        
        signal_bus = get_unified_signal_bus()
        
        if signal_bus.redis_client:
            # Test the signal bus Redis client
            test_key = "nexus:signal_bus:test"
            signal_bus.redis_client.set(test_key, "signal_bus_test", ex=10)
            value = signal_bus.redis_client.get(test_key)

            # Signal bus uses decode_responses=True, so value is string not bytes
            if value == "signal_bus_test":
                print("✅ NEXUS Signal Bus Redis: WORKING")
                signal_bus.redis_client.delete(test_key)
                return True
            else:
                print(f"❌ NEXUS Signal Bus Redis: READ/WRITE FAILED (got: {value})")
                return False
        else:
            print("❌ NEXUS Signal Bus Redis: CLIENT NOT INITIALIZED")
            return False
            
    except Exception as e:
        print(f"❌ NEXUS Signal Bus Error: {e}")
        return False

def main():
    """Main Redis health check"""
    print("🔧 REDIS HEALTH CHECK - RELIABLE VERSION")
    print("=" * 50)
    
    start_time = time.time()
    
    # Test 1: Direct Redis connection
    print("📡 Test 1: Direct Redis Connection")
    direct_result = test_redis_direct()
    
    print("\n📡 Test 2: NEXUS Signal Bus Redis")
    signal_bus_result = test_nexus_signal_bus()
    
    duration = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("📋 REDIS HEALTH CHECK RESULTS")
    print("=" * 50)
    print(f"✅ Direct Redis: {'PASS' if direct_result else 'FAIL'}")
    print(f"✅ Signal Bus Redis: {'PASS' if signal_bus_result else 'FAIL'}")
    print(f"⏱️ Duration: {duration:.2f} seconds")
    
    if direct_result and signal_bus_result:
        print("🎯 OVERALL STATUS: REDIS HEALTHY")
        print("🚀 Ready for Hummingbot integration testing")
        return 0
    else:
        print("🚨 OVERALL STATUS: REDIS ISSUES DETECTED")
        if not direct_result:
            print("💡 Fix: Start Redis server with 'redis-server'")
        if not signal_bus_result:
            print("💡 Fix: Check NEXUS signal bus configuration")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
