["tests/test_hummingbot_file_communication.py::TestAtomicFileOperations::test_basic_atomic_write_read", "tests/test_hummingbot_file_communication.py::TestAtomicFileOperations::test_concurrent_read_write", "tests/test_hummingbot_file_communication.py::TestAtomicFileOperations::test_concurrent_writes", "tests/test_hummingbot_file_communication.py::TestAtomicFileOperations::test_file_corruption_recovery", "tests/test_hummingbot_file_communication.py::TestAtomicFileOperations::test_nonexistent_file_handling", "tests/test_hummingbot_file_communication.py::TestAtomicFileOperations::test_partial_write_prevention", "tests/test_hummingbot_file_communication.py::TestAtomicFileOperations::test_temp_file_cleanup", "tests/test_hummingbot_file_communication.py::TestHummingbotCommunicationProtocol::test_command_format_validation", "tests/test_hummingbot_file_communication.py::TestHummingbotCommunicationProtocol::test_response_format_validation"]