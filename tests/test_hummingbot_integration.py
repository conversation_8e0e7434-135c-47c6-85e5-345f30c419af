#!/usr/bin/env python3
"""
🧪 HUMMINGBOT INTEGRATION TEST
End-to-end integration test for Hummingbot Enterprise Service Bus Bridge

TEST ARCHITECTURE:
NEXUS Signal Bus → Hummingbot Bridge → Hummingbot Script → Binance Testnet
                ← Execution Results ←                   ← Trade Confirmation ←

VALIDATION CRITERIA:
✅ Bridge connects to Redis signal bus
✅ Hummingbot container starts with NEXUS script
✅ Trading signal triggers order placement
✅ Order execution reported back to signal bus
✅ Market data flows from Hummingbot to NEXUS
✅ No mocks - real integration with testnet exchange

SAFETY:
- Uses Binance testnet only (paper trading)
- Small test amounts (0.01 BTC)
- Automatic cleanup after test
"""

import asyncio
import json
import time
import pytest
import logging
from pathlib import Path
from typing import Dict, Any

from src.agents.hummingbot_bridge import get_hummingbot_bridge
from src.agents.phase1_redis_signal_bus import get_unified_signal_bus, SignalType, SignalPriority
from src.core.system_orchestrator import SystemOrchestrator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HummingbotIntegrationTest:
    """Comprehensive Hummingbot integration test"""
    
    def __init__(self):
        self.signal_bus = get_unified_signal_bus()
        self.hummingbot_bridge = get_hummingbot_bridge()
        self.test_results = {}
        self.test_start_time = time.time()
        
        # Test configuration
        self.test_config = {
            'exchange': 'binance',  # Testnet
            'pair': 'BTC-USDT',
            'amount': 0.01,  # Small test amount
            'timeout': 300,  # 5 minutes max per test
        }
        
        logger.info("🧪 Hummingbot Integration Test initialized")
        logger.info(f"⚙️ Test config: {self.test_config}")
    
    async def run_full_integration_test(self) -> Dict[str, Any]:
        """Run complete end-to-end integration test"""
        logger.info("🚀 Starting Hummingbot Integration Test Suite")
        
        try:
            # Phase 1: Infrastructure Setup
            await self._test_infrastructure_setup()
            
            # Phase 2: Signal Bus Connection
            await self._test_signal_bus_connection()
            
            # Phase 3: Hummingbot Bridge Startup
            await self._test_bridge_startup()
            
            # Phase 4: Command Execution
            await self._test_command_execution()
            
            # Phase 5: Market Data Flow
            await self._test_market_data_flow()
            
            # Phase 6: System Integration
            await self._test_system_integration()
            
            # Generate final report
            return self._generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ Integration test failed: {e}")
            self.test_results['overall_status'] = 'FAILED'
            self.test_results['error'] = str(e)
            return self.test_results
        
        finally:
            await self._cleanup()
    
    async def _test_infrastructure_setup(self):
        """Test Phase 1: Infrastructure Setup"""
        logger.info("📋 Phase 1: Testing Infrastructure Setup")
        
        phase_results = {
            'redis_connection': False,
            'hummingbot_docker': False,
            'communication_files': False
        }
        
        # Test Redis connection
        try:
            await self.signal_bus.connect()
            phase_results['redis_connection'] = True
            logger.info("✅ Redis connection successful")
        except Exception as e:
            logger.error(f"❌ Redis connection failed: {e}")
            raise
        
        # Test Hummingbot Docker setup
        try:
            docker_path = Path("external/hummingbot/docker-compose.yml")
            if docker_path.exists():
                phase_results['hummingbot_docker'] = True
                logger.info("✅ Hummingbot Docker configuration found")
            else:
                raise Exception("Hummingbot Docker configuration not found")
        except Exception as e:
            logger.error(f"❌ Hummingbot Docker setup failed: {e}")
            raise
        
        # Test communication files
        try:
            script_path = Path("external/hummingbot/scripts/nexus_integration_script.py")
            if script_path.exists():
                phase_results['communication_files'] = True
                logger.info("✅ NEXUS integration script found")
            else:
                raise Exception("NEXUS integration script not found")
        except Exception as e:
            logger.error(f"❌ Communication files setup failed: {e}")
            raise
        
        self.test_results['phase_1_infrastructure'] = phase_results
        logger.info("✅ Phase 1: Infrastructure Setup - PASSED")
    
    async def _test_signal_bus_connection(self):
        """Test Phase 2: Signal Bus Connection"""
        logger.info("📡 Phase 2: Testing Signal Bus Connection")
        
        phase_results = {
            'channel_subscription': False,
            'signal_publishing': False,
            'signal_receiving': False
        }
        
        # Test channel subscription
        try:
            await self.signal_bus.subscribe_to_channel('trading_signals', self._test_signal_handler)
            phase_results['channel_subscription'] = True
            logger.info("✅ Channel subscription successful")
        except Exception as e:
            logger.error(f"❌ Channel subscription failed: {e}")
            raise
        
        # Test signal publishing
        try:
            test_signal = {
                'type': 'test',
                'timestamp': time.time(),
                'message': 'Integration test signal'
            }
            await self.signal_bus.publish_to_channel('trading_signals', test_signal)
            phase_results['signal_publishing'] = True
            logger.info("✅ Signal publishing successful")
        except Exception as e:
            logger.error(f"❌ Signal publishing failed: {e}")
            raise
        
        # Wait for signal reception (handled by _test_signal_handler)
        await asyncio.sleep(2)
        if hasattr(self, '_test_signal_received'):
            phase_results['signal_receiving'] = True
            logger.info("✅ Signal receiving successful")
        else:
            logger.warning("⚠️ Signal receiving not confirmed")
        
        self.test_results['phase_2_signal_bus'] = phase_results
        logger.info("✅ Phase 2: Signal Bus Connection - PASSED")
    
    async def _test_signal_handler(self, signal_data: Dict[str, Any]):
        """Test signal handler"""
        if signal_data.get('type') == 'test':
            self._test_signal_received = True
            logger.info(f"📨 Test signal received: {signal_data}")
    
    async def _test_bridge_startup(self):
        """Test Phase 3: Hummingbot Bridge Startup"""
        logger.info("🌉 Phase 3: Testing Hummingbot Bridge Startup")
        
        phase_results = {
            'bridge_initialization': False,
            'hummingbot_container_start': False,
            'script_loading': False
        }
        
        # Test bridge initialization
        try:
            # Start bridge in background
            bridge_task = asyncio.create_task(self.hummingbot_bridge.start())
            await asyncio.sleep(5)  # Give it time to initialize
            
            if self.hummingbot_bridge.is_running:
                phase_results['bridge_initialization'] = True
                logger.info("✅ Bridge initialization successful")
            else:
                raise Exception("Bridge failed to start")
        except Exception as e:
            logger.error(f"❌ Bridge initialization failed: {e}")
            raise
        
        # Test Hummingbot container start
        try:
            # Check if Docker container is running
            import subprocess
            result = subprocess.run(['docker', 'ps', '--filter', 'name=hummingbot'], 
                                  capture_output=True, text=True)
            if 'hummingbot' in result.stdout:
                phase_results['hummingbot_container_start'] = True
                logger.info("✅ Hummingbot container started")
            else:
                logger.warning("⚠️ Hummingbot container status unclear")
        except Exception as e:
            logger.warning(f"⚠️ Container check failed: {e}")
        
        # Test script loading (check for communication files)
        try:
            command_file = Path("external/hummingbot/nexus_commands.json")
            response_file = Path("external/hummingbot/nexus_responses.json")
            
            if command_file.exists() and response_file.exists():
                phase_results['script_loading'] = True
                logger.info("✅ Communication files created")
            else:
                logger.warning("⚠️ Communication files not found")
        except Exception as e:
            logger.warning(f"⚠️ Script loading check failed: {e}")
        
        self.test_results['phase_3_bridge_startup'] = phase_results
        logger.info("✅ Phase 3: Hummingbot Bridge Startup - PASSED")
    
    async def _test_command_execution(self):
        """Test Phase 4: Command Execution"""
        logger.info("⚡ Phase 4: Testing Command Execution")
        
        phase_results = {
            'buy_command': False,
            'order_placement': False,
            'execution_response': False
        }
        
        # Test buy command
        try:
            buy_signal = {
                'action': 'BUY',
                'pair': self.test_config['pair'],
                'amount': self.test_config['amount'],
                'exchange': self.test_config['exchange'],
                'order_type': 'market',
                'target_system': 'hummingbot',
                'timestamp': time.time()
            }
            
            # Subscribe to execution results
            await self.signal_bus.subscribe_to_channel('execution_results', self._execution_result_handler)
            
            # Send buy signal
            await self.signal_bus.publish_to_channel('trading_signals', buy_signal)
            phase_results['buy_command'] = True
            logger.info("✅ Buy command sent")
            
            # Wait for execution response
            timeout = self.test_config['timeout']
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                if hasattr(self, '_execution_received'):
                    phase_results['execution_response'] = True
                    logger.info("✅ Execution response received")
                    break
                await asyncio.sleep(1)
            
            if not phase_results['execution_response']:
                logger.warning("⚠️ Execution response timeout")
            
        except Exception as e:
            logger.error(f"❌ Command execution failed: {e}")
            raise
        
        self.test_results['phase_4_command_execution'] = phase_results
        logger.info("✅ Phase 4: Command Execution - COMPLETED")
    
    async def _execution_result_handler(self, result_data: Dict[str, Any]):
        """Handle execution results"""
        self._execution_received = True
        self._execution_data = result_data
        logger.info(f"📊 Execution result: {result_data}")
    
    async def _test_market_data_flow(self):
        """Test Phase 5: Market Data Flow"""
        logger.info("📊 Phase 5: Testing Market Data Flow")
        
        phase_results = {
            'market_data_subscription': False,
            'data_reception': False,
            'data_quality': False
        }
        
        try:
            # Subscribe to market data
            await self.signal_bus.subscribe_to_channel('hummingbot_market_data', self._market_data_handler)
            phase_results['market_data_subscription'] = True
            logger.info("✅ Market data subscription successful")
            
            # Wait for market data
            timeout = 60  # 1 minute
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                if hasattr(self, '_market_data_received'):
                    phase_results['data_reception'] = True
                    
                    # Check data quality
                    if self._validate_market_data(self._market_data):
                        phase_results['data_quality'] = True
                        logger.info("✅ Market data quality validated")
                    break
                await asyncio.sleep(2)
            
            if not phase_results['data_reception']:
                logger.warning("⚠️ Market data reception timeout")
            
        except Exception as e:
            logger.error(f"❌ Market data flow test failed: {e}")
        
        self.test_results['phase_5_market_data'] = phase_results
        logger.info("✅ Phase 5: Market Data Flow - COMPLETED")
    
    async def _market_data_handler(self, market_data: Dict[str, Any]):
        """Handle market data"""
        self._market_data_received = True
        self._market_data = market_data
        logger.info(f"📈 Market data received: {len(market_data)} exchanges")
    
    def _validate_market_data(self, market_data: Dict[str, Any]) -> bool:
        """Validate market data quality"""
        try:
            # Check if data contains expected structure
            if not isinstance(market_data, dict):
                return False
            
            # Check for exchange data
            for exchange, pairs in market_data.items():
                if not isinstance(pairs, dict):
                    continue
                
                for pair, data in pairs.items():
                    if not all(key in data for key in ['bid', 'ask', 'timestamp']):
                        continue
                    
                    # Basic sanity checks
                    if data['bid'] > 0 and data['ask'] > 0 and data['ask'] > data['bid']:
                        return True
            
            return False
        except Exception:
            return False
    
    async def _test_system_integration(self):
        """Test Phase 6: System Integration"""
        logger.info("🔗 Phase 6: Testing System Integration")
        
        phase_results = {
            'orchestrator_integration': False,
            'component_health': False,
            'end_to_end_flow': False
        }
        
        try:
            # Test orchestrator integration
            orchestrator = SystemOrchestrator()
            if 'hummingbot_bridge' in orchestrator.component_definitions:
                phase_results['orchestrator_integration'] = True
                logger.info("✅ Orchestrator integration confirmed")
            
            # Test component health
            if self.hummingbot_bridge.is_running:
                phase_results['component_health'] = True
                logger.info("✅ Component health confirmed")
            
            # Test end-to-end flow
            if (hasattr(self, '_execution_received') and 
                hasattr(self, '_market_data_received')):
                phase_results['end_to_end_flow'] = True
                logger.info("✅ End-to-end flow confirmed")
            
        except Exception as e:
            logger.error(f"❌ System integration test failed: {e}")
        
        self.test_results['phase_6_system_integration'] = phase_results
        logger.info("✅ Phase 6: System Integration - COMPLETED")
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = 0
        passed_tests = 0
        
        for phase, results in self.test_results.items():
            if isinstance(results, dict):
                for test, result in results.items():
                    total_tests += 1
                    if result:
                        passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            'test_suite': 'Hummingbot Integration Test',
            'timestamp': time.time(),
            'duration': time.time() - self.test_start_time,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'overall_status': 'PASSED' if success_rate >= 80 else 'FAILED',
            'detailed_results': self.test_results,
            'recommendations': self._generate_recommendations()
        }
        
        logger.info(f"📋 Test Report Generated:")
        logger.info(f"   Total Tests: {total_tests}")
        logger.info(f"   Passed: {passed_tests}")
        logger.info(f"   Success Rate: {success_rate:.1f}%")
        logger.info(f"   Overall Status: {report['overall_status']}")
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Analyze results and provide recommendations
        for phase, results in self.test_results.items():
            if isinstance(results, dict):
                failed_tests = [test for test, result in results.items() if not result]
                if failed_tests:
                    recommendations.append(f"Address failures in {phase}: {', '.join(failed_tests)}")
        
        if not recommendations:
            recommendations.append("All tests passed - system ready for production")
        
        return recommendations
    
    async def _cleanup(self):
        """Cleanup test resources"""
        logger.info("🧹 Cleaning up test resources...")
        
        try:
            # Stop bridge
            if self.hummingbot_bridge.is_running:
                await self.hummingbot_bridge.shutdown()
            
            # Clean up communication files
            files_to_clean = [
                "external/hummingbot/nexus_commands.json",
                "external/hummingbot/nexus_responses.json",
                "external/hummingbot/nexus_market_data.json"
            ]
            
            for file_path in files_to_clean:
                try:
                    Path(file_path).unlink(missing_ok=True)
                except Exception:
                    pass
            
            logger.info("✅ Cleanup completed")
            
        except Exception as e:
            logger.warning(f"⚠️ Cleanup warning: {e}")

# Test execution functions
async def run_integration_test():
    """Run the integration test"""
    test = HummingbotIntegrationTest()
    return await test.run_full_integration_test()

if __name__ == "__main__":
    # Run the test
    result = asyncio.run(run_integration_test())
    
    # Print results
    print("\n" + "="*60)
    print("🧪 HUMMINGBOT INTEGRATION TEST RESULTS")
    print("="*60)
    print(json.dumps(result, indent=2))
    print("="*60)
