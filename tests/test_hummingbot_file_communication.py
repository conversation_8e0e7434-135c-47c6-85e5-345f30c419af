#!/usr/bin/env python3
"""
🧪 HUMMINGBOT FILE COMMUNICATION UNIT TESTS
Tests atomic file I/O operations for Hummingbot bridge communication

CRITICAL TESTS:
- Atomic write operations under concurrent access
- File corruption prevention
- Race condition handling
- Partial write recovery
- File locking mechanisms

SAFETY:
- Uses temporary test files
- No real Hummingbot interaction
- Isolated test environment
"""

import asyncio
import json
import os
import tempfile
import time
import threading
import pytest
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any

# Test the atomic file operations directly
import sys
sys.path.append(str(Path(__file__).parent.parent))

class TestAtomicFileOperations:
    """Test atomic file I/O operations"""
    
    def setup_method(self):
        """Setup test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.command_file = self.test_dir / "test_commands.json"
        self.response_file = self.test_dir / "test_responses.json"
        
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def _atomic_write_json(self, file_path: Path, data: Dict[str, Any]):
        """Atomic write implementation (copied from bridge)"""
        temp_file = file_path.with_suffix('.tmp')
        try:
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
                f.flush()
                os.fsync(f.fileno())
            
            # Atomic move
            temp_file.replace(file_path)
            
        except Exception as e:
            if temp_file.exists():
                temp_file.unlink()
            raise e
    
    def _atomic_read_json(self, file_path: Path) -> Dict[str, Any]:
        """Atomic read implementation (copied from bridge)"""
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return None
        except Exception as e:
            raise e
    
    def test_basic_atomic_write_read(self):
        """Test basic atomic write and read operations"""
        test_data = {
            'command_id': 'test_123',
            'action': 'BUY',
            'pair': 'BTC-USDT',
            'amount': 0.01,
            'timestamp': time.time()
        }
        
        # Write data
        self._atomic_write_json(self.command_file, test_data)
        
        # Verify file exists
        assert self.command_file.exists()
        
        # Read data back
        read_data = self._atomic_read_json(self.command_file)
        
        # Verify data integrity
        assert read_data is not None
        assert read_data['command_id'] == test_data['command_id']
        assert read_data['action'] == test_data['action']
        assert read_data['pair'] == test_data['pair']
        assert read_data['amount'] == test_data['amount']
    
    def test_concurrent_writes(self):
        """Test concurrent write operations don't corrupt files"""
        num_writers = 10
        writes_per_writer = 5
        
        def writer_task(writer_id: int):
            """Writer task for concurrent testing"""
            results = []
            for i in range(writes_per_writer):
                test_data = {
                    'writer_id': writer_id,
                    'write_number': i,
                    'timestamp': time.time(),
                    'data': f"writer_{writer_id}_write_{i}"
                }
                
                try:
                    file_path = self.test_dir / f"concurrent_test_{writer_id}_{i}.json"
                    self._atomic_write_json(file_path, test_data)
                    
                    # Verify immediately
                    read_back = self._atomic_read_json(file_path)
                    if read_back and read_back['writer_id'] == writer_id:
                        results.append(True)
                    else:
                        results.append(False)
                        
                except Exception as e:
                    print(f"Writer {writer_id} error: {e}")
                    results.append(False)
                
                # Small delay to increase chance of race conditions
                time.sleep(0.001)
            
            return results
        
        # Run concurrent writers
        with ThreadPoolExecutor(max_workers=num_writers) as executor:
            futures = [
                executor.submit(writer_task, writer_id) 
                for writer_id in range(num_writers)
            ]
            
            # Collect results
            all_results = []
            for future in futures:
                all_results.extend(future.result())
        
        # Verify all writes succeeded
        success_rate = sum(all_results) / len(all_results)
        assert success_rate >= 0.95, f"Success rate too low: {success_rate:.2%}"
    
    def test_concurrent_read_write(self):
        """Test concurrent read/write operations"""
        test_file = self.test_dir / "read_write_test.json"
        
        # Initialize file
        initial_data = {'counter': 0, 'operations': []}
        self._atomic_write_json(test_file, initial_data)
        
        num_operations = 20
        results = {'reads': [], 'writes': []}
        
        def reader_task():
            """Reader task"""
            for i in range(num_operations):
                try:
                    data = self._atomic_read_json(test_file)
                    if data and 'counter' in data:
                        results['reads'].append(True)
                    else:
                        results['reads'].append(False)
                except Exception:
                    results['reads'].append(False)
                time.sleep(0.001)
        
        def writer_task():
            """Writer task"""
            for i in range(num_operations):
                try:
                    # Read current data
                    current_data = self._atomic_read_json(test_file)
                    if current_data:
                        # Update data
                        current_data['counter'] += 1
                        current_data['operations'].append(f'write_{i}')
                        
                        # Write back
                        self._atomic_write_json(test_file, current_data)
                        results['writes'].append(True)
                    else:
                        results['writes'].append(False)
                except Exception:
                    results['writes'].append(False)
                time.sleep(0.001)
        
        # Run concurrent operations
        reader_thread = threading.Thread(target=reader_task)
        writer_thread = threading.Thread(target=writer_task)
        
        reader_thread.start()
        writer_thread.start()
        
        reader_thread.join()
        writer_thread.join()
        
        # Verify results
        read_success_rate = sum(results['reads']) / len(results['reads'])
        write_success_rate = sum(results['writes']) / len(results['writes'])
        
        assert read_success_rate >= 0.90, f"Read success rate too low: {read_success_rate:.2%}"
        assert write_success_rate >= 0.90, f"Write success rate too low: {write_success_rate:.2%}"
        
        # Verify final file integrity
        final_data = self._atomic_read_json(test_file)
        assert final_data is not None
        assert final_data['counter'] == num_operations
        assert len(final_data['operations']) == num_operations
    
    def test_file_corruption_recovery(self):
        """Test recovery from corrupted files"""
        test_file = self.test_dir / "corruption_test.json"
        
        # Create corrupted file (invalid JSON)
        with open(test_file, 'w') as f:
            f.write('{"invalid": json content')
        
        # Attempt to read corrupted file
        result = self._atomic_read_json(test_file)
        assert result is None  # Should handle corruption gracefully
        
        # Write valid data over corrupted file
        valid_data = {'status': 'recovered', 'timestamp': time.time()}
        self._atomic_write_json(test_file, valid_data)
        
        # Verify recovery
        recovered_data = self._atomic_read_json(test_file)
        assert recovered_data is not None
        assert recovered_data['status'] == 'recovered'
    
    def test_partial_write_prevention(self):
        """Test that partial writes are prevented"""
        test_file = self.test_dir / "partial_write_test.json"
        
        # Large data to increase chance of partial write
        large_data = {
            'command_id': 'large_test',
            'large_field': 'x' * 10000,  # 10KB of data
            'timestamp': time.time(),
            'metadata': {f'key_{i}': f'value_{i}' for i in range(1000)}
        }
        
        # Write large data
        self._atomic_write_json(test_file, large_data)
        
        # Read back and verify integrity
        read_data = self._atomic_read_json(test_file)
        
        assert read_data is not None
        assert read_data['command_id'] == large_data['command_id']
        assert len(read_data['large_field']) == 10000
        assert len(read_data['metadata']) == 1000
        
        # Verify all metadata
        for i in range(1000):
            assert read_data['metadata'][f'key_{i}'] == f'value_{i}'
    
    def test_nonexistent_file_handling(self):
        """Test handling of nonexistent files"""
        nonexistent_file = self.test_dir / "does_not_exist.json"
        
        # Attempt to read nonexistent file
        result = self._atomic_read_json(nonexistent_file)
        assert result is None
        
        # Write to nonexistent file (should create it)
        test_data = {'created': True, 'timestamp': time.time()}
        self._atomic_write_json(nonexistent_file, test_data)
        
        # Verify file was created and data is correct
        assert nonexistent_file.exists()
        read_data = self._atomic_read_json(nonexistent_file)
        assert read_data is not None
        assert read_data['created'] is True
    
    def test_temp_file_cleanup(self):
        """Test that temporary files are cleaned up"""
        test_file = self.test_dir / "temp_cleanup_test.json"
        temp_file = test_file.with_suffix('.tmp')
        
        # Normal operation should not leave temp files
        test_data = {'cleanup_test': True}
        self._atomic_write_json(test_file, test_data)
        
        # Verify temp file doesn't exist
        assert not temp_file.exists()
        assert test_file.exists()
        
        # Simulate write failure by making directory read-only
        # (This test may be platform-specific)
        try:
            # Make temp file creation fail
            os.chmod(self.test_dir, 0o444)  # Read-only
            
            with pytest.raises(Exception):
                self._atomic_write_json(test_file, {'should_fail': True})
            
            # Restore permissions
            os.chmod(self.test_dir, 0o755)
            
            # Verify no temp files left behind
            temp_files = list(self.test_dir.glob('*.tmp'))
            assert len(temp_files) == 0
            
        except (OSError, PermissionError):
            # Skip this test on systems where chmod doesn't work as expected
            os.chmod(self.test_dir, 0o755)  # Restore permissions
            pytest.skip("Cannot test temp file cleanup on this system")

class TestHummingbotCommunicationProtocol:
    """Test the communication protocol between bridge and Hummingbot"""
    
    def setup_method(self):
        """Setup test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.command_file = self.test_dir / "nexus_commands.json"
        self.response_file = self.test_dir / "nexus_responses.json"
        
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_command_format_validation(self):
        """Test command format validation"""
        # Valid command
        valid_command = {
            'timestamp': time.time(),
            'command_id': 'test_123',
            'action': 'BUY',
            'pair': 'BTC-USDT',
            'amount': 0.01,
            'exchange': 'binance',
            'order_type': 'market'
        }
        
        # Verify all required fields are present
        required_fields = ['timestamp', 'command_id', 'action', 'pair', 'amount']
        for field in required_fields:
            assert field in valid_command
        
        # Verify data types
        assert isinstance(valid_command['timestamp'], (int, float))
        assert isinstance(valid_command['command_id'], str)
        assert valid_command['action'] in ['BUY', 'SELL', 'CANCEL', 'STATUS', 'BALANCE']
        assert isinstance(valid_command['pair'], str)
        assert isinstance(valid_command['amount'], (int, float))
    
    def test_response_format_validation(self):
        """Test response format validation"""
        # Valid response
        valid_response = {
            'command_id': 'test_123',
            'order_id': 'hb_order_456',
            'status': 'placed',
            'action': 'BUY',
            'pair': 'BTC-USDT',
            'amount': 0.01,
            'price': 50000.0,
            'exchange': 'binance',
            'timestamp': time.time()
        }
        
        # Verify required fields
        required_fields = ['command_id', 'status', 'timestamp']
        for field in required_fields:
            assert field in valid_response
        
        # Verify status values
        valid_statuses = ['placed', 'filled', 'cancelled', 'error', 'partial']
        assert valid_response['status'] in valid_statuses

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
