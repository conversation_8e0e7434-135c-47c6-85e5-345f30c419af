services:
  hummingbot:
    container_name: hummingbot
    image: hummingbot/hummingbot:latest
    build:  # Uncomment this and comment image if you want to build it locally
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./conf:/home/<USER>/conf
      - ./conf/connectors:/home/<USER>/conf/connectors
      - ./conf/strategies:/home/<USER>/conf/strategies
      - ./conf/controllers:/home/<USER>/conf/controllers
      - ./conf/scripts:/home/<USER>/conf/scripts
      - ./logs:/home/<USER>/logs
      - ./data:/home/<USER>/data
      - ./certs:/home/<USER>/certs
      - ./scripts:/home/<USER>/scripts
      - ./controllers:/home/<USER>/controllers
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    tty: true
    stdin_open: true
    network_mode: host
    environment:
      - CONFIG_PASSWORD=nexus123
      - CONFIG_FILE_NAME=nexus_integration_script.py
      - SCRIPT_CONFIG=conf_nexus_integration.yml

  # gateway:
  #  restart: always
  #  container_name: gateway
  #  image: hummingbot/gateway:latest
  #  ports:
  #    - "15888:15888"
  #    - "8080:8080"
  #  volumes:
  #    - "./gateway_files/conf:/home/<USER>/conf"
  #    - "./gateway_files/logs:/home/<USER>/logs"
  #    - "./gateway_files/db:/home/<USER>/db"
  #    - "./certs:/home/<USER>/certs"
  #  environment:
  #    - GATEWAY_PASSPHRASE=a
