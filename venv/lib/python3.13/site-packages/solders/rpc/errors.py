from typing import Union

from ..solders import (
    BlockCleanedUp,
    BlockCleanedUpMessage,
    BlockNotAvailable,
    BlockNotAvailableMessage,
    BlockStatusNotAvailableYet,
    BlockStatusNotAvailableYetMessage,
    InternalErrorMessage,
    InvalidParamsMessage,
    InvalidRequestMessage,
    KeyExcludedFromSecondaryIndex,
    KeyExcludedFromSecondaryIndexMessage,
    LongTermStorageSlotSkipped,
    LongTermStorageSlotSkippedMessage,
    MethodNotFoundMessage,
    MinContextSlotNotReached,
    MinContextSlotNotReachedMessage,
    NodeUnhealthy,
    NodeUnhealthyMessage,
    ParseErrorMessage,
    RpcCustomErrorFieldless,
    ScanError,
    ScanErrorMessage,
    SendTransactionPreflightFailure,
    SendTransactionPreflightFailureMessage,
    SlotSkipped,
    SlotSkippedMessage,
    TransactionPrecompileVerificationFailure,
    TransactionPrecompileVerificationFailureMessage,
    UnsupportedTransactionVersion,
    UnsupportedTransactionVersionMessage,
)

RpcCustomError = Union[
    RpcCustomErrorFieldless,
    BlockCleanedUp,
    SendTransactionPreflightFailure,
    BlockNotAvailable,
    NodeUnhealthy,
    TransactionPrecompileVerificationFailure,
    SlotSkipped,
    LongTermStorageSlotSkipped,
    BlockCleanedUp,
    KeyExcludedFromSecondaryIndex,
    ScanError,
    BlockStatusNotAvailableYet,
    MinContextSlotNotReached,
    UnsupportedTransactionVersion,
]

__all__ = [
    "RpcCustomError",
    "BlockCleanedUp",
    "BlockCleanedUpMessage",
    "SendTransactionPreflightFailure",
    "SendTransactionPreflightFailureMessage",
    "RpcCustomErrorFieldless",
    "BlockNotAvailable",
    "BlockNotAvailableMessage",
    "NodeUnhealthy",
    "NodeUnhealthyMessage",
    "TransactionPrecompileVerificationFailure",
    "TransactionPrecompileVerificationFailureMessage",
    "SlotSkipped",
    "SlotSkippedMessage",
    "LongTermStorageSlotSkipped",
    "LongTermStorageSlotSkippedMessage",
    "KeyExcludedFromSecondaryIndex",
    "KeyExcludedFromSecondaryIndexMessage",
    "ScanError",
    "ScanErrorMessage",
    "BlockStatusNotAvailableYet",
    "BlockStatusNotAvailableYetMessage",
    "MinContextSlotNotReached",
    "MinContextSlotNotReachedMessage",
    "UnsupportedTransactionVersion",
    "UnsupportedTransactionVersionMessage",
    "ParseErrorMessage",
    "InvalidRequestMessage",
    "MethodNotFoundMessage",
    "InvalidParamsMessage",
    "InternalErrorMessage",
]
